const express = require("express");
const cors = require("cors");
const helmet = require("helmet");
const { status: httpStatus } = require("http-status");
const morgan = require("./config/morgan");
const passport = require('./config/passport');
const apiLimiter = require("./middlewares/rateLimiter");
const routes = require("./routes");
const { errorConverter, errorHandler } = require("./middlewares/error");
const { ApiError } = require("./helpers/api.helper");
const config = require("./config/config");
const { tykLogging, tykErrorLogger } = require("./middlewares/tykLogger");

const app = express();

// request logging
app.use(morgan.successHandler);
app.use(morgan.errorHandler);

// Tyk-specific logging (only applied when Tyk is enabled)
if (config.tyk.enabled) {
  app.use(tykLogging);
}

// set security HTTP headers
app.use(helmet());
// parse json request body
app.use(express.json());

// parse urlencoded request body
app.use(express.urlencoded({ extended: true }));

// enable cors
app.use(cors());
app.options("*", cors());

// passport authentication
app.use(passport.initialize());

// limit repeated failed requests
app.use("/", apiLimiter);

// api routes
app.use("/", routes);

// Handle WebSocket endpoint requests (to prevent error logs)
app.all("/ws", (req, res) => {
  res.status(404).json({
    status: false,
    message: "WebSocket endpoint not available",
    note: "This application does not support WebSocket connections"
  });
});

// send back a 404 error for any unknown api request
app.use((req, res, next) => {
  next(new ApiError(httpStatus.NOT_FOUND, "Not found"));
});

// Tyk error logging (only applied when Tyk is enabled)
if (config.tyk.enabled) {
  app.use(tykErrorLogger);
}

// convert error to ApiError, if needed
app.use(errorConverter);

// handle error
app.use(errorHandler);

module.exports = app;
