const passport = require('../config/passport');
const { status: httpStatus } = require("http-status");
const { ApiError } = require("../helpers/api.helper");
const config = require('../config/config');
const logger = require('../config/logger');

// The verifyCallback now uses permissions directly from the JWT token.
const verifyCallback = (req, resolve, reject, requiredRights) => async (err, identity, info) => {
  if (err || info || !identity) {
    return reject(
      new ApiError(httpStatus.UNAUTHORIZED, "Please authenticate")
    );
  }

  req.identity = identity;

  if (requiredRights.length) {
    // Get permissions directly from the JWT token (added by passport strategy)
    const identityRights = identity.permissions || [];

    if (identityRights.length === 0) {
      return reject(
        new ApiError(httpStatus.FORBIDDEN, "No permissions assigned to identity")
      );
    }

    // Check required rights
    const hasRequiredRights = requiredRights.every((requiredRight) =>
      identityRights.includes(requiredRight)
    );

    if (
      !hasRequiredRights &&
      req.params.identityId !== identity.identity_id
    ) {
      return reject(new ApiError(httpStatus.FORBIDDEN, "Unauthorized access of identity"));
    }
  }

  resolve();
};

/**
 * Determine which authentication strategy to use based on configuration
 * @returns {string} - Authentication strategy name
 */
const getAuthStrategy = () => {
  // Check if Tyk mode is enabled
  if (config.auth.mode === 'tyk' && config.tyk.enabled) {
    return 'tyk';
  }
  return 'custom';
};

/**
 * Enhanced auth middleware that supports both custom and Tyk authentication
 * @param {...string} requiredRights - Required permissions for the route
 * @returns {Function} - Express middleware function
 */
const auth = (...requiredRights) =>
  async (req, res, next) => {
    const strategy = getAuthStrategy();

    // Log authentication attempt
    logger.info('Authentication attempt', {
      strategy,
      requiredRights,
      userAgent: req.headers['user-agent'],
      ip: req.ip,
      path: req.path,
      method: req.method
    });

    return new Promise((resolve, reject) => {
      passport.authenticate(
        strategy,
        { session: false },
        verifyCallback(req, resolve, reject, requiredRights)
      )(req, res, next);
    })
      .then(() => {
        // Log successful authentication
        logger.info('Authentication successful', {
          strategy,
          identityId: req.identity?.identity_id,
          email: req.identity?.email,
          authMethod: req.identity?.auth_method,
          permissions: req.identity?.permissions?.length || 0
        });
        next();
      })
      .catch((err) => {
        // Log authentication failure
        logger.warn('Authentication failed', {
          strategy,
          error: err.message,
          userAgent: req.headers['user-agent'],
          ip: req.ip,
          path: req.path
        });
        next(err);
      });
  };

/**
 * Middleware to force custom authentication (bypass Tyk)
 * Useful for internal endpoints that should always use custom auth
 */
const authCustom = (...requiredRights) =>
  async (req, res, next) => {
    logger.info('Forced custom authentication', {
      requiredRights,
      path: req.path
    });

    return new Promise((resolve, reject) => {
      passport.authenticate(
        "custom",
        { session: false },
        verifyCallback(req, resolve, reject, requiredRights)
      )(req, res, next);
    })
      .then(() => next())
      .catch((err) => next(err));
  };

/**
 * Middleware to force Tyk authentication
 * Useful for endpoints that should always use Tyk auth
 */
const authTyk = (...requiredRights) =>
  async (req, res, next) => {
    if (!config.tyk.enabled) {
      return next(new ApiError(httpStatus.SERVICE_UNAVAILABLE, "Tyk authentication is not enabled"));
    }

    logger.info('Forced Tyk authentication', {
      requiredRights,
      path: req.path
    });

    return new Promise((resolve, reject) => {
      passport.authenticate(
        "tyk",
        { session: false },
        verifyCallback(req, resolve, reject, requiredRights)
      )(req, res, next);
    })
      .then(() => next())
      .catch((err) => next(err));
  };

/**
 * Middleware for optional authentication
 * Continues even if authentication fails, but populates req.identity if successful
 */
const authOptional = (...requiredRights) =>
  async (req, res, next) => {
    const strategy = getAuthStrategy();

    logger.info('Optional authentication attempt', {
      strategy,
      path: req.path
    });

    return new Promise((resolve, reject) => {
      passport.authenticate(
        strategy,
        { session: false },
        (err, identity, info) => {
          if (err) {
            logger.warn('Optional authentication error', { error: err.message });
            return resolve(); // Continue without authentication
          }

          if (identity) {
            req.identity = identity;
            logger.info('Optional authentication successful', {
              identityId: identity.identity_id
            });
          } else {
            logger.info('Optional authentication failed, continuing without auth', {
              info: info?.message
            });
          }

          resolve();
        }
      )(req, res, next);
    })
      .then(() => next())
      .catch((err) => next(err));
  };

// Export the main auth function as default for backward compatibility
module.exports = auth;

// Export additional auth functions as properties
module.exports.auth = auth;
module.exports.authCustom = authCustom;
module.exports.authTyk = authTyk;
module.exports.authOptional = authOptional;
module.exports.getAuthStrategy = getAuthStrategy;
