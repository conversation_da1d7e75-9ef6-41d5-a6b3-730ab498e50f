# CareMate API - Tyk Integration Configuration Example
# Copy this file to .env and configure the values according to your setup

# Basic Configuration
NODE_ENV=development
PORT=3000
SERVER_URL=http://localhost:3000

# Database Configuration
DB_DIALECT=postgres
DB_WRITE_HOST=localhost
DB_WRITE_USERNAME=your_db_user
DB_WRITE_PASSWORD=your_db_password
DB_WRITE_DATABASE=caremate_db
DB_READ_HOST=localhost
DB_READ_USERNAME=your_db_user
DB_READ_PASSWORD=your_db_password
DB_READ_DATABASE=caremate_db
DB_LOGGING=false

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here
JWT_ACCESS_EXPIRATION_MINUTES=30
JWT_REFRESH_EXPIRATION_DAYS=30

# Authentication Mode Configuration
# Set to 'custom' for direct authentication or 'tyk_proxy' for Tyk reverse proxy mode
# In tyk_proxy mode, <PERSON><PERSON> handles rate limiting and analytics while preserving custom auth
AUTH_MODE=tyk_proxy

# Tyk Reverse Proxy Configuration
# Enable Tyk API Gateway as reverse proxy for rate limiting and analytics
TYK_ENABLED=true

# Tyk Gateway URL (where your Tyk Gateway is running)
TYK_GATEWAY_URL=http://localhost:8080

# Tyk Gateway Secret (x-tyk-authorization header value)
TYK_GATEWAY_SECRET=your-tyk-gateway-secret

# Tyk API ID (unique identifier for your API in Tyk)
TYK_API_ID=caremate-api-v1

# Tyk Organization ID
TYK_ORG_ID=your-org-id

# Tyk Listen Path (the path Tyk will listen on)
TYK_LISTEN_PATH=/api/v1

# Target URL (where Tyk should proxy requests to - your API server)
TYK_TARGET_URL=http://localhost:3000

# Tyk Rate Limiting Configuration
TYK_RATE_LIMIT_ENABLED=true
TYK_RATE_LIMIT_PER=60
TYK_RATE_LIMIT_RATE=1000

# Tyk Quota Configuration
TYK_QUOTA_ENABLED=false
TYK_QUOTA_MAX=10000
TYK_QUOTA_RENEWAL_RATE=3600

# Logging Configuration
LOG_LEVEL=info
LOG_MAX_SIZE=20m
LOG_MAX_FILES=30d
LOG_FILE_TRANSPORT=true

# Cache Configuration
CACHE_DRIVER=redis
CACHE_TTL=60
REDIS_HOST=localhost
REDIS_PORT=6379

# Message Queuing Configuration
MESSAGE_QUEUING=true
RABBITMQ_URL=amqp://localhost:5672

# Message Processing Configuration
CONCURRENCY_LIMIT=15
COLLECTION_TIMEOUT=3000
SHUTDOWN_TIMEOUT=30000

# Email Configuration
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-email-password

# SMS Configuration (Telnyx)
TELNYX_API_KEY=your-telnyx-api-key
TELNYX_PHONE_NUMBER=your-telnyx-phone-number

# SAML Configuration (Optional)
SAML_ENTRY_POINT=https://your-idp.com/saml/sso
SAML_ISSUER=caremate-api
SAML_CERT=your-saml-certificate
SAML_CALLBACK_URL=http://localhost:3000/auth/saml/callback

# OIDC Configuration (Optional)
OIDC_ISSUER=https://your-oidc-provider.com
OIDC_AUTHORIZATION_URL=https://your-oidc-provider.com/auth
OIDC_TOKEN_URL=https://your-oidc-provider.com/token
OIDC_USERINFO_URL=https://your-oidc-provider.com/userinfo
OIDC_CLIENT_ID=your-oidc-client-id
OIDC_CLIENT_SECRET=your-oidc-client-secret
OIDC_CALLBACK_URL=http://localhost:3000/auth/oidc/callback

# Azure AD Configuration (Optional)
AZURE_TENANT_ID=your-azure-tenant-id
AZURE_CLIENT_ID=your-azure-client-id
AZURE_CLIENT_SECRET=your-azure-client-secret
AZURE_CALLBACK_URL=http://localhost:3000/auth/azure/callback

# Memcached Configuration (if using memcached for caching)
MEMCACHED_HOST=localhost:11211
