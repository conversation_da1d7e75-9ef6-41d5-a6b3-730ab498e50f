# Tyk Integration Quick Start Guide

This guide will help you quickly set up and test the Tyk integration with your CareMate API.

## Prerequisites

1. **Tyk Gateway** installed and running (or access to Tyk Cloud)
2. **Node.js** application running
3. **Basic understanding** of API Gateway concepts

## Quick Setup (5 minutes)

### Step 1: Install Dependencies

The required dependencies are already included in package.json:
- `passport-custom` - For Tyk authentication strategy
- `axios` - For Tyk API communication

```bash
npm install
```

### Step 2: Configure Environment Variables

Copy the example configuration:
```bash
cp .env.tyk.example .env
```

Update these key variables in your `.env` file:
```bash
# Switch to Tyk mode
AUTH_MODE=tyk
TYK_ENABLED=true

# Tyk Gateway Configuration
TYK_GATEWAY_URL=http://localhost:8080
TYK_GATEWAY_SECRET=your-tyk-secret
TYK_API_ID=caremate-api
TYK_ORG_ID=your-org-id
TYK_TARGET_URL=http://localhost:3000
```

### Step 3: Create Tyk API Definition

Use the Tyk Dashboard or API to create an API definition:

```json
{
  "name": "CareMate API",
  "api_id": "caremate-api",
  "org_id": "your-org-id",
  "use_keyless": false,
  "auth": {
    "auth_header_name": "Authorization"
  },
  "proxy": {
    "listen_path": "/api/v1/",
    "target_url": "http://localhost:3000",
    "strip_listen_path": true
  },
  "active": true,
  "custom_middleware": {
    "auth_check": {
      "name": "TykCustomAuth"
    },
    "driver": "grpc"
  }
}
```

### Step 4: Start Your Application

```bash
npm start
```

### Step 5: Test the Integration

Test with Tyk headers:
```bash
curl -H "x-tyk-api-key: your-api-key" \
     -H "x-tyk-user-email: <EMAIL>" \
     -H "x-tyk-user-roles: user" \
     http://localhost:8080/api/v1/health
```

## Testing Both Modes

### Test Custom Authentication
```bash
# Set AUTH_MODE=custom in .env
curl -H "Authorization: Bearer your-jwt-token" \
     http://localhost:3000/health
```

### Test Tyk Authentication
```bash
# Set AUTH_MODE=tyk in .env
curl -H "x-tyk-api-key: your-api-key" \
     -H "x-tyk-user-email: <EMAIL>" \
     http://localhost:8080/api/v1/health
```

## Common Tyk Headers

When Tyk forwards requests, it can include these headers:

```bash
x-tyk-api-key: "abc123..."           # API key used
x-tyk-user-email: "<EMAIL>" # User email
x-tyk-user-roles: "admin,user"       # User roles
x-tyk-user-id: "user123"             # User ID
x-ratelimit-remaining: "95"          # Rate limit remaining
x-ratelimit-limit: "100"             # Rate limit maximum
x-ratelimit-reset: "**********"      # Reset timestamp
```

## Verification Checklist

- [ ] Application starts without errors
- [ ] Logs show "Tyk authentication headers received" for Tyk requests
- [ ] Rate limiting respects Tyk headers
- [ ] Authentication works in both modes
- [ ] Permissions are correctly mapped from Tyk roles

## Troubleshooting

### Issue: "Tyk authentication is not enabled"
**Solution**: Check `TYK_ENABLED=true` and `AUTH_MODE=tyk` in .env

### Issue: No Tyk headers received
**Solution**: 
1. Verify Tyk API definition includes custom middleware
2. Check Tyk is forwarding requests to correct target URL
3. Ensure API key is valid in Tyk

### Issue: Rate limiting not working
**Solution**:
1. Check `TYK_RATE_LIMIT_ENABLED=true`
2. Verify Tyk is sending rate limit headers
3. Check logs for rate limit information

### Issue: Permissions not working
**Solution**:
1. Check role mapping in `config/passport.js`
2. Verify `x-tyk-user-roles` header is being sent
3. Check logs for permission assignment

## Advanced Configuration

### Custom Role Mapping
Edit `config/passport.js` to customize role-to-permission mapping:

```javascript
switch (role.toLowerCase()) {
  case 'admin':
    permissions.push(...require('./permissions'));
    break;
  case 'manager':
    permissions.push('view_facilities', 'edit_facility');
    break;
  case 'viewer':
    permissions.push('view_facilities');
    break;
}
```

### Custom Rate Limiting
Use different rate limiters for different endpoints:

```javascript
const { hybridRateLimit } = require('../middlewares/rateLimiter');

// Use Tyk limits if available, otherwise local limits
router.use('/api', hybridRateLimit({ max: 1000, windowMs: 60000 }));
```

### Logging Configuration
Adjust logging levels in .env:

```bash
LOG_LEVEL=debug  # For detailed Tyk header logging
LOG_LEVEL=info   # For normal operation
```

## Production Considerations

1. **Security**: Ensure Tyk Gateway secret is secure
2. **Performance**: Monitor rate limiting effectiveness
3. **Monitoring**: Set up log aggregation for Tyk events
4. **Backup**: Keep custom auth as fallback option
5. **Testing**: Test both authentication modes regularly

## Next Steps

1. **Read Full Documentation**: See `docs/TYK_INTEGRATION.md`
2. **Run Tests**: Execute `npm test tests/tyk-integration.test.js`
3. **Monitor Logs**: Check application logs for Tyk events
4. **Customize**: Adapt role mapping and permissions to your needs
5. **Scale**: Consider Tyk clustering for production

## Support

For issues with:
- **Tyk Configuration**: Check Tyk documentation
- **Integration Code**: Review logs and test files
- **Authentication**: Verify headers and role mapping
- **Rate Limiting**: Check Tyk and local configurations

## Quick Commands Reference

```bash
# Switch to Tyk mode
echo "AUTH_MODE=tyk" >> .env

# Switch to Custom mode  
echo "AUTH_MODE=custom" >> .env

# Test health endpoint
curl http://localhost:3000/health

# Test with Tyk headers
curl -H "x-tyk-api-key: test" http://localhost:8080/api/v1/health

# Check logs
tail -f logs/api/apilog-$(date +%Y-%m-%d).log

# Run integration tests
npm test tests/tyk-integration.test.js
```
