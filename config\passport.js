const passport = require("passport");
const { Strategy: JwtStrategy, ExtractJwt } = require("passport-jwt");
const SamlStrategy = require("passport-saml").Strategy;
const OpenIDConnectStrategy = require("passport-openidconnect").Strategy;
const { OIDCStrategy: AzureOIDCStrategy } = require("passport-azure-ad");
const config = require("./config");
const { Identity } = require("../models");
const { tokenTypes } = require("./attributes");
const tykConfig = require("./tyk");
const logger = require("./logger");

// ---------------------
// Email & Password Strategy
// ---------------------
const jwtOptions = {
  secretOrKey: config.jwt.secret,
  jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
};
const jwtVerify = async (payload, done) => {
  try {
    if (payload.type !== tokenTypes.ACCESS) {
      throw new Error("Invalid token type");
    }
    const identity = await Identity.findByPk(payload.sub);
    if (!identity) {
      return done(null, false);
    }

    // Add permissions from JWT payload to identity object for easy access
    identity.permissions = payload.permissions || [];

    done(null, identity);
  } catch (error) {
    done(error, false);
  }
};
passport.use("custom", new JwtStrategy(jwtOptions, jwtVerify));

// ---------------------
// Tyk Authentication Strategy
// ---------------------
const TykStrategy = require("passport-custom").Strategy;

const tykVerify = async (req, done) => {
  try {
    // Check if Tyk is enabled
    if (!tykConfig.isEnabled()) {
      return done(new Error("Tyk authentication is not enabled"), false);
    }

    // Extract Tyk headers that are typically added by Tyk Gateway
    const tykHeaders = {
      apiKey: req.headers['x-tyk-api-key'] || req.headers['authorization']?.replace('Bearer ', ''),
      apiId: req.headers['x-tyk-api-id'],
      orgId: req.headers['x-tyk-org-id'],
      userId: req.headers['x-tyk-user-id'],
      userEmail: req.headers['x-tyk-user-email'],
      userRoles: req.headers['x-tyk-user-roles'],
      sessionAlias: req.headers['x-tyk-session-alias'],
      rateLimitRemaining: req.headers['x-ratelimit-remaining'],
      rateLimitLimit: req.headers['x-ratelimit-limit'],
      rateLimitReset: req.headers['x-ratelimit-reset']
    };

    // Log Tyk headers for debugging
    logger.info('Tyk authentication headers received', {
      headers: tykHeaders,
      userAgent: req.headers['user-agent'],
      ip: req.ip
    });

    // If no API key is provided, authentication fails
    if (!tykHeaders.apiKey) {
      return done(null, false, { message: 'No API key provided' });
    }

    // Validate the API key with Tyk (optional - Tyk should have already validated it)
    let keyValidation;
    try {
      keyValidation = await tykConfig.validateApiKey(tykHeaders.apiKey);
      if (!keyValidation.valid) {
        return done(null, false, { message: 'Invalid API key' });
      }
    } catch (error) {
      logger.warn('Failed to validate API key with Tyk, proceeding with header-based auth', {
        error: error.message
      });
      // Continue with header-based authentication if Tyk validation fails
      // This allows for cases where Tyk has already validated the key
    }

    // Try to find existing identity by email or user ID
    let identity = null;

    if (tykHeaders.userEmail) {
      identity = await Identity.findOne({
        where: { email: tykHeaders.userEmail }
      });
    } else if (tykHeaders.userId) {
      identity = await Identity.findByPk(tykHeaders.userId);
    }

    // If no identity found, create a virtual identity for Tyk users
    if (!identity) {
      // Create a virtual identity object (not saved to database)
      identity = {
        identity_id: tykHeaders.userId || `tyk_${tykHeaders.apiKey.substring(0, 8)}`,
        email: tykHeaders.userEmail || `tyk_user_${tykHeaders.apiKey.substring(0, 8)}@tyk.local`,
        first_name: 'Tyk',
        last_name: 'User',
        is_active: true,
        // Add Tyk-specific metadata
        tyk_metadata: {
          api_key: tykHeaders.apiKey,
          api_id: tykHeaders.apiId,
          org_id: tykHeaders.orgId,
          session_alias: tykHeaders.sessionAlias,
          rate_limit: {
            remaining: tykHeaders.rateLimitRemaining,
            limit: tykHeaders.rateLimitLimit,
            reset: tykHeaders.rateLimitReset
          }
        }
      };
    }

    // Add permissions based on Tyk user roles or default permissions
    const permissions = [];
    if (tykHeaders.userRoles) {
      const roles = tykHeaders.userRoles.split(',').map(role => role.trim());
      // Map Tyk roles to permissions (customize based on your needs)
      roles.forEach(role => {
        switch (role.toLowerCase()) {
          case 'admin':
            permissions.push(...require('./permissions'));
            break;
          case 'user':
            permissions.push('view_facilities', 'view_appointments', 'view_patients');
            break;
          case 'readonly':
            permissions.push('view_facilities');
            break;
          default:
            // Default permissions for unknown roles
            permissions.push('view_facilities');
        }
      });
    } else {
      // Default permissions when no roles are specified
      permissions.push('view_facilities');
    }

    // Remove duplicates and add to identity
    identity.permissions = [...new Set(permissions)];

    // Add Tyk-specific properties
    identity.auth_method = 'tyk';
    identity.tyk_headers = tykHeaders;

    logger.info('Tyk authentication successful', {
      identityId: identity.identity_id,
      email: identity.email,
      permissions: identity.permissions,
      authMethod: identity.auth_method
    });

    done(null, identity);
  } catch (error) {
    logger.error('Tyk authentication error', {
      error: error.message,
      stack: error.stack
    });
    done(error, false);
  }
};

passport.use("tyk", new TykStrategy(tykVerify));

// ---------------------
// SAML Strategy
// ---------------------
passport.use(
  "saml",
  new SamlStrategy(
    {
      path: config.saml.callbackUrl,
      entryPoint: config.saml.entryPoint,
      issuer: config.saml.issuer,
      cert: config.saml.cert,
    },
    async (profile, done) => {
      try {
        // Lookup the user by a unique attribute (e.g. email) from the SAML profile.
        let identity = await Identity.findOne({
          where: { email: profile.email },
        });
        if (!identity) {
          identity = await Identity.create({
            username: profile.nameID,
            email: profile.email,
            first_name: profile.firstName,
            last_name: profile.lastName,
          });
        }
        return done(null, identity);
      } catch (error) {
        return done(error, false);
      }
    }
  )
);

// ---------------------
// OpenID Connect Strategy
// ---------------------
passport.use(
  "oidc",
  new OpenIDConnectStrategy(
    {
      issuer: config.oidc.issuer,
      authorizationURL: config.oidc.authorizationURL,
      tokenURL: config.oidc.tokenURL,
      userInfoURL: config.oidc.userInfoURL,
      clientID: config.oidc.clientID,
      clientSecret: config.oidc.clientSecret,
      callbackURL: config.oidc.callbackURL,
    },
    async (issuer, sub, profile, accessToken, refreshToken, done) => {
      try {
        let identity = await Identity.findOne({
          where: { email: profile.email },
        });
        if (!identity) {
          identity = await Identity.create({
            username: profile.displayName || profile.email,
            email: profile.email,
            first_name: profile.given_name,
            last_name: profile.family_name,
          });
        }
        return done(null, identity);
      } catch (error) {
        return done(error, false);
      }
    }
  )
);

// ---------------------
// Azure AD Strategy (OIDC)
// ---------------------
passport.use(
  "azure",
  new AzureOIDCStrategy(
    {
      identityMetadata: config.azure.identityMetadata,
      clientID: config.azure.clientID,
      responseType: config.azure.responseType,
      responseMode: config.azure.responseMode,
      redirectUrl: config.azure.callbackURL,
      allowHttpForRedirectUrl: config.azure.allowHttpForRedirectUrl,
      clientSecret: config.azure.clientSecret,
    },
    async (iss, sub, profile, accessToken, refreshToken, done) => {
      try {
        // Note: The structure of the profile from Azure AD is found in profile._json.
        let identity = await Identity.findOne({
          where: { email: profile._json.email },
        });
        if (!identity) {
          identity = await Identity.create({
            username: profile.displayName || profile._json.email,
            email: profile._json.email,
            first_name: profile._json.given_name,
            last_name: profile._json.family_name,
          });
        }
        return done(null, identity);
      } catch (error) {
        return done(error, false);
      }
    }
  )
);

module.exports = passport;
