const request = require('supertest');
const app = require('../app');
const config = require('../config/config');
const tykConfig = require('../config/tyk');
const { auth, getAuthStrategy } = require('../middlewares/auth');

describe('Tyk Reverse Proxy Integration Tests', () => {
  
  describe('Configuration Tests', () => {
    test('should have Tyk configuration structure', () => {
      expect(config.auth).toBeDefined();
      expect(config.auth.mode).toBeDefined();
      expect(config.tyk).toBeDefined();
      expect(config.tyk.enabled).toBeDefined();
    });

    test('should validate Tyk configuration when enabled', () => {
      const originalEnabled = config.tyk.enabled;
      
      // Test with Tyk disabled
      config.tyk.enabled = false;
      const disabledValidation = tykConfig.validateConfig();
      expect(disabledValidation.valid).toBe(true);
      expect(disabledValidation.message).toContain('disabled');

      // Restore original value
      config.tyk.enabled = originalEnabled;
    });

    test('should determine correct authentication strategy', () => {
      const originalAuthMode = config.auth.mode;
      const originalTykEnabled = config.tyk.enabled;

      // Test custom mode
      config.auth.mode = 'custom';
      config.tyk.enabled = false;
      expect(getAuthStrategy()).toBe('custom');

      // Test Tyk reverse proxy mode
      config.auth.mode = 'tyk_proxy';
      config.tyk.enabled = true;
      expect(getAuthStrategy()).toBe('tyk');

      // Test fallback to custom when Tyk is disabled
      config.auth.mode = 'tyk_proxy';
      config.tyk.enabled = false;
      expect(getAuthStrategy()).toBe('custom');

      // Restore original values
      config.auth.mode = originalAuthMode;
      config.tyk.enabled = originalTykEnabled;
    });
  });

  describe('Authentication Strategy Tests', () => {
    test('should handle custom authentication', async () => {
      // This test would require a valid JWT token
      // For now, we'll test that the endpoint responds appropriately
      const response = await request(app)
        .get('/health')
        .expect(200);
      
      expect(response.body).toBeDefined();
    });

    test('should handle Tyk reverse proxy headers with JWT', async () => {
      // Test with Tyk headers and original JWT authorization
      const response = await request(app)
        .get('/health')
        .set('x-tyk-api-id', 'caremate-api')
        .set('x-tyk-org-id', 'test-org')
        .set('x-ratelimit-remaining', '100')
        .set('x-ratelimit-limit', '1000')
        .set('authorization', 'Bearer test-jwt-token')
        .expect(200);

      expect(response.body).toBeDefined();
    });
  });

  describe('Rate Limiting Tests', () => {
    test('should respect Tyk rate limit headers', async () => {
      const response = await request(app)
        .get('/health')
        .set('x-ratelimit-remaining', '100')
        .set('x-ratelimit-limit', '1000')
        .set('x-ratelimit-reset', '**********')
        .expect(200);
      
      expect(response.body).toBeDefined();
    });

    test('should handle rate limit exceeded from Tyk', async () => {
      // This would be handled by Tyk in a real scenario
      // We're testing our middleware's ability to process the headers
      const response = await request(app)
        .get('/health')
        .set('x-ratelimit-remaining', '0')
        .set('x-ratelimit-limit', '1000')
        .expect(200); // Health endpoint doesn't require auth
      
      expect(response.body).toBeDefined();
    });
  });

  describe('Logging Tests', () => {
    test('should extract Tyk headers correctly', () => {
      const { extractTykHeaders } = require('../middlewares/tykLogger');
      
      const mockReq = {
        headers: {
          'x-tyk-api-key': 'test-key',
          'x-tyk-user-email': '<EMAIL>',
          'x-ratelimit-remaining': '100',
          'authorization': 'Bearer token',
          'user-agent': 'test-agent'
        }
      };

      const tykHeaders = extractTykHeaders(mockReq);
      
      expect(tykHeaders['x-tyk-api-key']).toBe('test-key');
      expect(tykHeaders['x-tyk-user-email']).toBe('<EMAIL>');
      expect(tykHeaders['x-ratelimit-remaining']).toBe('100');
      expect(tykHeaders['authorization']).toBeUndefined(); // Should not include non-Tyk headers
      expect(tykHeaders['user-agent']).toBeUndefined(); // Should not include non-Tyk headers
    });
  });

  describe('Health Check Tests', () => {
    test('should return health status', async () => {
      const response = await request(app)
        .get('/health')
        .expect(200);
      
      expect(response.body).toBeDefined();
      expect(response.body.status).toBeDefined();
    });
  });

  describe('Error Handling Tests', () => {
    test('should handle 404 errors', async () => {
      const response = await request(app)
        .get('/non-existent-endpoint')
        .expect(404);
      
      expect(response.body).toBeDefined();
      expect(response.body.message).toContain('Not found');
    });
  });

  describe('Middleware Integration Tests', () => {
    test('should apply middlewares in correct order', () => {
      // Test that our middleware exports are correct
      expect(typeof auth).toBe('function');
      expect(typeof getAuthStrategy).toBe('function');
    });

    test('should handle authentication middleware options', () => {
      const { authCustom, authTyk, authOptional } = require('../middlewares/auth');
      
      expect(typeof authCustom).toBe('function');
      expect(typeof authTyk).toBe('function');
      expect(typeof authOptional).toBe('function');
    });
  });

  describe('Tyk Configuration Class Tests', () => {
    test('should create TykConfig instance', () => {
      expect(tykConfig).toBeDefined();
      expect(typeof tykConfig.isEnabled).toBe('function');
      expect(typeof tykConfig.validateConfig).toBe('function');
    });

    test('should handle Tyk health check when disabled', async () => {
      const originalEnabled = config.tyk.enabled;
      config.tyk.enabled = false;
      
      const health = await tykConfig.getHealth();
      expect(health.status).toBe('disabled');
      
      config.tyk.enabled = originalEnabled;
    });
  });

  describe('Environment Variable Tests', () => {
    test('should have required environment variables structure', () => {
      // Test that the configuration accepts the expected environment variables
      expect(config.auth.mode).toBeDefined();
      expect(['custom', 'tyk'].includes(config.auth.mode)).toBe(true);
    });
  });

  describe('Passport Strategy Tests', () => {
    test('should have passport strategies configured', () => {
      const passport = require('../config/passport');
      expect(passport).toBeDefined();
      
      // Test that strategies are registered (this is implicit through require)
      expect(true).toBe(true); // Placeholder - actual strategy testing would require more setup
    });
  });
});

// Helper functions for testing
const createMockTykRequest = (overrides = {}) => {
  return {
    headers: {
      'x-tyk-api-key': 'test-api-key',
      'x-tyk-user-email': '<EMAIL>',
      'x-tyk-user-roles': 'user',
      'x-ratelimit-remaining': '100',
      'x-ratelimit-limit': '1000',
      ...overrides
    },
    ip: '127.0.0.1',
    path: '/test',
    method: 'GET'
  };
};

const createMockCustomRequest = (overrides = {}) => {
  return {
    headers: {
      'authorization': 'Bearer test-jwt-token',
      ...overrides
    },
    ip: '127.0.0.1',
    path: '/test',
    method: 'GET'
  };
};

module.exports = {
  createMockTykRequest,
  createMockCustomRequest
};
