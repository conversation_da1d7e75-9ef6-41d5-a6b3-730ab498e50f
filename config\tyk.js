const config = require('./config');
const logger = require('./logger');
const axios = require('axios');

/**
 * Tyk API Gateway Reverse Proxy Configuration and Utilities
 * This module provides configuration and utility functions for Tyk as a reverse proxy
 * Tyk handles rate limiting, analytics, and request forwarding while preserving
 * the existing custom authentication system with dynamic roles and permissions
 */

class TykConfig {
  constructor() {
    this.enabled = config.tyk.enabled;
    this.gatewayUrl = config.tyk.gatewayUrl;
    this.gatewaySecret = config.tyk.gatewaySecret;
    this.apiId = config.tyk.apiId;
    this.orgId = config.tyk.orgId;
    this.listenPath = config.tyk.listenPath;
    this.targetUrl = config.tyk.targetUrl;
    this.rateLimit = config.tyk.rateLimit;
    this.quota = config.tyk.quota;
    
    // Initialize axios instance for Tyk API calls
    this.tykApi = axios.create({
      baseURL: this.gatewayUrl,
      headers: {
        'x-tyk-authorization': this.gatewaySecret,
        'Content-Type': 'application/json'
      },
      timeout: 10000
    });
  }

  /**
   * Check if Tyk is enabled and properly configured
   * @returns {boolean}
   */
  isEnabled() {
    return this.enabled && this.gatewayUrl && this.gatewaySecret;
  }

  /**
   * Validate Tyk configuration
   * @returns {Object} validation result
   */
  validateConfig() {
    const errors = [];
    
    if (!this.enabled) {
      return { valid: true, message: 'Tyk is disabled' };
    }

    if (!this.gatewayUrl) {
      errors.push('TYK_GATEWAY_URL is required when Tyk is enabled');
    }
    
    if (!this.gatewaySecret) {
      errors.push('TYK_GATEWAY_SECRET is required when Tyk is enabled');
    }
    
    if (!this.apiId) {
      errors.push('TYK_API_ID is required when Tyk is enabled');
    }
    
    if (!this.orgId) {
      errors.push('TYK_ORG_ID is required when Tyk is enabled');
    }
    
    if (!this.targetUrl) {
      errors.push('TYK_TARGET_URL is required when Tyk is enabled');
    }

    return {
      valid: errors.length === 0,
      errors: errors,
      message: errors.length > 0 ? errors.join(', ') : 'Tyk configuration is valid'
    };
  }

  /**
   * Create or update API definition in Tyk as reverse proxy
   * @returns {Promise<Object>}
   */
  async createApiDefinition() {
    if (!this.isEnabled()) {
      throw new Error('Tyk reverse proxy is not enabled or properly configured');
    }

    const apiDefinition = {
      name: `CareMate API Reverse Proxy - ${config.env}`,
      api_id: this.apiId,
      org_id: this.orgId,
      use_keyless: true, // No authentication at Tyk level - pass through to backend
      definition: {
        location: "header",
        key: "x-api-version"
      },
      version_data: {
        not_versioned: true,
        versions: {
          "Default": {
            name: "Default",
            use_extended_paths: true,
            extended_paths: {
              ignored: [],
              white_list: [],
              black_list: []
            }
          }
        }
      },
      proxy: {
        listen_path: this.listenPath,
        target_url: this.targetUrl,
        strip_listen_path: true,
        preserve_host_header: false,
        // Forward original authorization header
        transform: {
          template_data: {
            template_mode: "blob",
            template_source: ""
          }
        }
      },
      active: true,
      enable_batch_request_support: false,
      enable_ip_whitelisting: false,
      enable_ip_blacklisting: false,
      enable_context_vars: true,
      // Add headers for analytics and identification
      global_headers: {
        "X-Tyk-API-ID": this.apiId,
        "X-Tyk-Org-ID": this.orgId
      },
      // Custom middleware for header injection and analytics
      custom_middleware: {
        pre: [
          {
            name: "TykAnalyticsHeaders",
            require_session: false
          }
        ],
        post: [],
        post_key_auth: [],
        response: [
          {
            name: "TykResponseAnalytics",
            require_session: false
          }
        ],
        driver: "grpc"
      }
    };

    // Add rate limiting if enabled
    if (this.rateLimit.enabled) {
      apiDefinition.version_data.versions.Default.global_rate_limit = {
        rate: this.rateLimit.rate,
        per: this.rateLimit.per
      };
    }

    // Add quota if enabled
    if (this.quota.enabled) {
      apiDefinition.version_data.versions.Default.quota = {
        quota_max: this.quota.max,
        quota_renewal_rate: this.quota.renewalRate
      };
    }

    try {
      const response = await this.tykApi.post('/tyk/apis/', apiDefinition);
      logger.info('Tyk API definition created/updated successfully', {
        apiId: this.apiId,
        status: response.data.Status
      });
      return response.data;
    } catch (error) {
      logger.error('Failed to create/update Tyk API definition', {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  /**
   * Reload Tyk configuration
   * @returns {Promise<Object>}
   */
  async reloadTyk() {
    if (!this.isEnabled()) {
      throw new Error('Tyk is not enabled or properly configured');
    }

    try {
      const response = await this.tykApi.get('/tyk/reload/group');
      logger.info('Tyk configuration reloaded successfully');
      return response.data;
    } catch (error) {
      logger.error('Failed to reload Tyk configuration', {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  /**
   * Get API definition from Tyk
   * @returns {Promise<Object>}
   */
  async getApiDefinition() {
    if (!this.isEnabled()) {
      throw new Error('Tyk is not enabled or properly configured');
    }

    try {
      const response = await this.tykApi.get(`/tyk/apis/${this.apiId}`);
      return response.data;
    } catch (error) {
      logger.error('Failed to get Tyk API definition', {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  /**
   * Validate API key with Tyk
   * @param {string} apiKey - The API key to validate
   * @returns {Promise<Object>}
   */
  async validateApiKey(apiKey) {
    if (!this.isEnabled()) {
      throw new Error('Tyk is not enabled or properly configured');
    }

    try {
      const response = await this.tykApi.get(`/tyk/keys/${apiKey}?api_id=${this.apiId}`);
      return {
        valid: true,
        keyData: response.data
      };
    } catch (error) {
      if (error.response?.status === 404) {
        return {
          valid: false,
          error: 'API key not found'
        };
      }
      
      logger.error('Failed to validate API key with Tyk', {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  /**
   * Create API key in Tyk
   * @param {Object} keyData - Key configuration
   * @returns {Promise<Object>}
   */
  async createApiKey(keyData) {
    if (!this.isEnabled()) {
      throw new Error('Tyk is not enabled or properly configured');
    }

    const defaultKeyData = {
      allowance: this.rateLimit.rate,
      rate: this.rateLimit.rate,
      per: this.rateLimit.per,
      expires: -1,
      quota_max: this.quota.enabled ? this.quota.max : -1,
      quota_renews: this.quota.enabled ? Math.floor(Date.now() / 1000) + this.quota.renewalRate : 0,
      quota_remaining: this.quota.enabled ? this.quota.max : -1,
      quota_renewal_rate: this.quota.enabled ? this.quota.renewalRate : 0,
      access_rights: {
        [this.apiId]: {
          api_id: this.apiId,
          api_name: `CareMate API - ${config.env}`,
          versions: ["Default"]
        }
      },
      org_id: this.orgId,
      meta_data: {},
      ...keyData
    };

    try {
      const response = await this.tykApi.post('/tyk/keys/create', defaultKeyData);
      logger.info('Tyk API key created successfully', {
        keyId: response.data.key_id
      });
      return response.data;
    } catch (error) {
      logger.error('Failed to create Tyk API key', {
        error: error.message,
        response: error.response?.data
      });
      throw error;
    }
  }

  /**
   * Get Tyk health status
   * @returns {Promise<Object>}
   */
  async getHealth() {
    if (!this.isEnabled()) {
      return { status: 'disabled', message: 'Tyk is not enabled' };
    }

    try {
      const response = await this.tykApi.get('/hello');
      return {
        status: 'healthy',
        message: response.data.message || 'Tyk is running',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('Tyk health check failed', {
        error: error.message
      });
      return {
        status: 'unhealthy',
        message: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const tykConfig = new TykConfig();

module.exports = tykConfig;
