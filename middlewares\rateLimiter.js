const rateLimit = require('express-rate-limit');
const config = require('../config/config');
const logger = require('../config/logger');

/**
 * Smart rate limiter that works with both custom and Tyk authentication modes
 */

// Default rate limiter configuration
const defaultRateLimiterConfig = {
  windowMs: 15 * 60 * 1000,  // 15 minutes
  max: 10000,  // limit each IP to 10000 requests per window
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
};

// Create the default rate limiter
const defaultApiLimiter = rateLimit(defaultRateLimiterConfig);

/**
 * Conditional rate limiter middleware
 * Applies rate limiting based on authentication mode and configuration
 */
const apiLimiter = (req, res, next) => {
  // If Tyk is enabled and handling rate limiting, skip our rate limiter
  if (config.tyk.enabled && config.tyk.rateLimit.enabled) {
    logger.debug('Skipping local rate limiting - Tyk is handling rate limits', {
      path: req.path,
      method: req.method,
      tykEnabled: config.tyk.enabled,
      tykRateLimitEnabled: config.tyk.rateLimit.enabled
    });

    // Log Tyk rate limit headers if present
    const tykRateLimit = {
      remaining: req.headers['x-ratelimit-remaining'],
      limit: req.headers['x-ratelimit-limit'],
      reset: req.headers['x-ratelimit-reset']
    };

    if (tykRateLimit.remaining || tykRateLimit.limit) {
      logger.debug('Tyk rate limit headers detected', {
        tykRateLimit,
        path: req.path
      });
    }

    return next();
  }

  // Apply local rate limiting for custom authentication or when Tyk rate limiting is disabled
  logger.debug('Applying local rate limiting', {
    path: req.path,
    method: req.method,
    authMode: config.auth.mode,
    tykEnabled: config.tyk.enabled
  });

  return defaultApiLimiter(req, res, next);
};

/**
 * Force local rate limiting regardless of Tyk configuration
 * Useful for specific endpoints that need additional protection
 */
const forceLocalRateLimit = (options = {}) => {
  const rateLimiterConfig = {
    ...defaultRateLimiterConfig,
    ...options
  };

  return rateLimit(rateLimiterConfig);
};

/**
 * Strict rate limiter for sensitive endpoints
 */
const strictApiLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,  // 15 minutes
  max: 100,  // limit each IP to 100 requests per window
  message: 'Too many requests to sensitive endpoint, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Auth rate limiter for login/register endpoints
 */
const authRateLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,  // 15 minutes
  max: 50,  // limit each IP to 50 auth requests per window
  message: 'Too many authentication attempts, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
  skipSuccessfulRequests: true, // Don't count successful requests
});

/**
 * Rate limiter that respects Tyk headers
 * Uses Tyk rate limit information if available, otherwise applies local limits
 */
const hybridRateLimit = (localConfig = {}) => {
  const localLimiter = rateLimit({
    ...defaultRateLimiterConfig,
    ...localConfig
  });

  return (req, res, next) => {
    // Check if Tyk rate limit headers are present
    const tykRemaining = parseInt(req.headers['x-ratelimit-remaining'], 10);
    const tykLimit = parseInt(req.headers['x-ratelimit-limit'], 10);

    if (!isNaN(tykRemaining) && !isNaN(tykLimit)) {
      // Tyk headers are present, use them for rate limiting decisions
      if (tykRemaining <= 0) {
        logger.warn('Request blocked by Tyk rate limit', {
          remaining: tykRemaining,
          limit: tykLimit,
          path: req.path,
          ip: req.ip
        });

        return res.status(429).json({
          error: 'Rate limit exceeded',
          message: 'Too many requests, please try again later.',
          retryAfter: req.headers['x-ratelimit-reset']
        });
      }

      // Add Tyk rate limit info to response headers
      res.set({
        'X-RateLimit-Limit': tykLimit,
        'X-RateLimit-Remaining': tykRemaining,
        'X-RateLimit-Reset': req.headers['x-ratelimit-reset']
      });

      logger.debug('Using Tyk rate limit headers', {
        remaining: tykRemaining,
        limit: tykLimit,
        path: req.path
      });

      return next();
    }

    // No Tyk headers, apply local rate limiting
    logger.debug('No Tyk rate limit headers, applying local rate limiting', {
      path: req.path
    });

    return localLimiter(req, res, next);
  };
};

module.exports = apiLimiter;

// Export additional rate limiters
module.exports.apiLimiter = apiLimiter;
module.exports.forceLocalRateLimit = forceLocalRateLimit;
module.exports.strictApiLimiter = strictApiLimiter;
module.exports.authRateLimiter = authRateLimiter;
module.exports.hybridRateLimit = hybridRateLimit;

